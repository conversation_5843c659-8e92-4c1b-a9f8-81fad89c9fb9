import React, { useState, useEffect } from 'react';
import { useParams, useLocation, useNavigate } from 'react-router-dom';
import EstimateSummaryPage from './EstimateSummaryPage.tsx';
import { useClaimsContext } from '../../ClaimsWrapper.tsx';
import { useClaimStore, useFileStore } from '../../../../store';
import { EstimateItem, EstimateCreateProduct } from '../../../../store/claims/types/Claim.ts';
import FlashMessages from '../../../app/shared/FlashMessages.tsx';

const EstimateSummaryPageWrapper: React.FC = () => {
    const params = useParams();
    const location = useLocation();
    const navigate = useNavigate();
    const { currentClaim } = useClaimsContext();
    const { setEstimate, getClaimEstimateDocumentLink } = useClaimStore();
    const { uploadFileS3 } = useFileStore();
    const [items, setItems] = useState<EstimateItem[]>([]);

    // Get data from location state (when navigating from CreateEstimatePage)
    useEffect(() => {
        if (location.state) {
            const { items: stateItems } = location.state as any;
            if (stateItems && Array.isArray(stateItems)) {
                setItems(stateItems);
            } else {
                // If no items provided, redirect back to create page
                navigate(`/claims/${params.type}/${params.id}/estimate/create`);
            }
        } else {
            // If no state provided, redirect back to create page
            navigate(`/claims/${params.type}/${params.id}/estimate/create`);
        }
    }, [location.state, navigate, params.type, params.id]);

    const handleSubmitForApproval = async () => {
        if (!currentClaim?.id) {
            FlashMessages.error('No claim selected');
            return;
        }

        try {
            // Get form data and file info from location state
            const { formData, newUploadedFile, currentFile, estimate } = location.state as any;

            // Process form data into estimate products
            const estimateProducts: EstimateCreateProduct[] = [];

            if (formData) {
                Object.keys(formData).forEach((groupName) => {
                    const groupData = formData[groupName];
                    
                    if (Array.isArray(groupData) && groupData.length > 0) {
                        groupData.forEach((item: any) => {
                            if (item && item.pricebook_entry_id && item.product_id && item.unit_price && item.quantity) {
                                const product: EstimateCreateProduct = {
                                    pricebook_entry_id: item.pricebook_entry_id,
                                    product_id: item.product_id,
                                    unit_price: parseFloat(item.unit_price.toString().replace(/,/g, '')),
                                    quantity: parseInt(item.quantity.toString()),
                                };

                                // Add length for Storage items
                                if (groupName === 'Storage' && item.length) {
                                    (product as any).length = parseInt(item.length.toString());
                                }

                                estimateProducts.push(product);
                            }
                        });
                    }
                });
            }

            if (estimateProducts.length === 0) {
                FlashMessages.error('No estimate items found');
                return;
            }

            // Upload file if there's a new one
            let fileUrl = '';
            if (newUploadedFile) {
                const documentLinkResponse = await getClaimEstimateDocumentLink(currentClaim.id, newUploadedFile.name);
                await uploadFileS3(newUploadedFile, documentLinkResponse.presigned_url);
                fileUrl = documentLinkResponse.url;
            } else if (typeof currentFile === 'string') {
                fileUrl = currentFile;
            }

            // Create or update estimate
            const estimateData = {
                original_estimate: fileUrl,
                products: estimateProducts,
            };

            await setEstimate(currentClaim.id, estimateData);
            
            FlashMessages.success(estimate?.id ? 'Estimate updated successfully!' : 'Estimate created successfully!');
            navigate(`/claims/${params.type}/${params.id}`);

        } catch (error) {
            console.error('Error submitting estimate:', error);
            FlashMessages.error('Failed to save estimate. Please try again.');
        }
    };

    if (items.length === 0) {
        return <div>Loading...</div>;
    }

    return (
        <EstimateSummaryPage
            items={items}
            onSubmitForApproval={handleSubmitForApproval}
        />
    );
};

export default EstimateSummaryPageWrapper;
