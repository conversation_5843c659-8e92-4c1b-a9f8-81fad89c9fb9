import React, { useEffect, useState } from 'react';
import Content from '../../../app/layouts/content/Content.tsx';
import CustomForm from '../../../app/shared/form/Form.tsx';
import { Form } from 'antd';
import Button, { ButtonVariation } from '../../../app/shared/Button.tsx';
import ClaimStatusTag from '../../../app/shared/ClaimStatusTag.tsx';
import {
    Estimate,
    PriceBookItem,
} from '../../../../store/claims/types/Claim.ts';
import { useClaimStore } from '../../../../store';
import { useClaimsContext } from '../../ClaimsWrapper.tsx';

import Loader from '../../../app/shared/Loader.tsx';
import classNames from 'classnames';
import { PriceBookSortingGroups } from './utils.ts';
import Dialog from '../../../app/shared/Dialog.tsx';
import FlashMessages from '../../../app/shared/FlashMessages.tsx';

import EstimateConfirmationModal from './EstimateConfirmationModal.tsx';

import { useNavigate, useParams } from 'react-router-dom';

interface Props {
    onChangeFile?: (newFile: File) => void;
    uploadedFile: File | string;
    estimate?: Estimate;
}

const CreateEstimatePage: React.FC<Props> = ({
    estimate,
    uploadedFile,
}) => {
    const navigate = useNavigate();
    const params = useParams();
    const [form] = Form.useForm();
    const formValues = Form.useWatch([], form);
    const { getClaimPriceBook } = useClaimStore();
    const { currentClaim } = useClaimsContext();
    const [groupedProducts, setGroupedProducts] = useState<Record<string, PriceBookItem[]>>({});
    const [canSubmit, setCanSubmit] = useState<boolean>(true);
    const [infoExpanded, setInfoExpanded] = useState<boolean>(false);
    const [showConfirmModal, setShowConfirmModal] = useState<boolean>(false);
    const [priceBookLoading, setPriceBookLoading] = useState<boolean>(false);
    const [selectedTab, setSelectedTab] = useState<string>('');
    const [showConfirmationModal, setShowConfirmationModal] = useState<boolean>(false);
    const [newUploadedFile] = useState<File | null>(null);
    const [currentFile] = useState<File | string>(uploadedFile);

    const groupNames = Object.keys(groupedProducts);

    const onClose = () => {
        navigate(`/claims/${params.type}/${params.id}`);
    };

    const onSuccess = () => {
        navigate(`/claims/${params.type}/${params.id}`);
    };

    // Load price book data
    useEffect(() => {
        if (!currentClaim?.id) {
            return;
        }

        setPriceBookLoading(true);
        getClaimPriceBook(currentClaim.id)
            .then((data) => {
                console.log('Price book data received:', data);
                
                // Group products by product_family and sort according to PriceBookSortingGroups
                const grouped = data.reduce<Record<string, PriceBookItem[]>>((acc, product) => {
                    acc[product.product_family] = acc[product.product_family] || [];
                    acc[product.product_family].push(product);
                    return acc;
                }, {});

                // Sort the groups according to PriceBookSortingGroups order
                const sortedGrouped: Record<string, PriceBookItem[]> = {};
                PriceBookSortingGroups.forEach(groupName => {
                    if (grouped[groupName]) {
                        sortedGrouped[groupName] = grouped[groupName];
                    }
                });

                // Add any remaining groups that weren't in the sorting list
                Object.keys(grouped).forEach(groupName => {
                    if (!PriceBookSortingGroups.includes(groupName)) {
                        sortedGrouped[groupName] = grouped[groupName];
                    }
                });

                console.log('Grouped and sorted products:', sortedGrouped);
                setGroupedProducts(sortedGrouped);
            })
            .catch((error) => {
                console.error('Error loading price book:', error);
                FlashMessages.error('Failed to load price book data');
            })
            .finally(() => {
                setPriceBookLoading(false);
            });
    }, [currentClaim?.id, getClaimPriceBook]);

    // TODO: Load existing estimate data if editing
    // This will be implemented when the form structure is complete

    // Set initial selected tab
    useEffect(() => {
        if (!selectedTab && Object.keys(groupedProducts).length > 0) {
            const firstTab = Object.keys(groupedProducts)[0];
            console.log('Setting initial selectedTab to:', firstTab);
            setSelectedTab(firstTab);
        }
    }, [groupedProducts]);

    // Debug form values
    useEffect(() => {
        console.log('Form values changed:', formValues);
        console.log('Selected tab:', selectedTab);
    }, [formValues, selectedTab]);

    const onSubmit = async (values: any) => {
        console.log('Form submission values:', values);

        if (!currentClaim?.id) {
            FlashMessages.error('No claim selected');
            return;
        }

        setCanSubmit(false);

        try {
            // TODO: Implement form submission logic
            console.log('Form submitted with values:', values);
            FlashMessages.success('Form submitted successfully!');
            onSuccess();
        } catch (error) {
            console.error('Error submitting form:', error);
            FlashMessages.error('Failed to submit form. Please try again.');
        } finally {
            setCanSubmit(true);
        }
    };

    const handleEstimateSummary = () => {
        console.log('Navigating to estimate summary');

        // Get form data
        const allFormValues = form.getFieldsValue();
        console.log('Form values for summary:', allFormValues);

        // Navigate to summary page with the form data
        navigate(`/claims/${params.type}/${params.id}/estimate/summary`, {
            state: {
                items: [], // TODO: Process form data into items
                formData: allFormValues,
                newUploadedFile,
                currentFile,
                estimate
            }
        });
    };

    const handleConfirmationYes = () => {
        setShowConfirmationModal(false);
        handleEstimateSummary();
    };

    const handleConfirmationNo = () => {
        setShowConfirmationModal(false);
    };

    const handlePreviewFile = () => {
        console.log('Preview file:', currentFile);
        // TODO: Implement file preview
    };

    const handleChangeFile = () => {
        console.log('Change file');
        // TODO: Implement file change
    };





    return (
        <Content
            headerTitle="Add Services"
            breadcrumbItems={[
                { title: 'Projects', href: `/claims/${params.type}` },
                { title: currentClaim?.claim_name || 'Claim', href: `/claims/${params.type}/${params.id}` },
                { title: 'Add Services' },
            ]}
        >
            <div className="claim-ce" style={{ display: 'flex', gap: '24px' }}>
                {/* Side content equivalent */}
                <div className="claim-ce-sidebar" style={{ flex: '0 0 300px', background: '#f8f9fa', padding: '20px', borderRadius: '8px' }}>
                    <div className="claim-header">
                        <h1 className="heading h3">{currentClaim?.claim_name}</h1>
                        <ClaimStatusTag status={currentClaim?.status} />
                    </div>

                    <div className="claim-ce-content">
                        <div className="claim-ce-info" style={{ marginBottom: '0px' }}>
                            <div
                                className={classNames('claim-ce-info-inner', {
                                    expanded: infoExpanded,
                                })}
                                style={{ background: 'none', paddingTop: '0px' }}
                            >
                                <h5 className="claim-ce-info-title">Create your estimate</h5>
                                <div className="claim-ce-info-group">
                                    <p className="claim-ce-info-group-description">
                                        Now it's time to input your line items. Using our
                                        service page, select a service and enter the
                                        corresponding rate and quantity.
                                        <div
                                            className="claim-ce-info-action"
                                            onClick={() => setInfoExpanded(!infoExpanded)}
                                        >
                                            {infoExpanded ? 'Less' : 'Learn More'}
                                        </div>
                                    </p>
                                </div>

                                {infoExpanded && (
                                    <>
                                        <div className="claim-ce-info-group">
                                            <h6 className="claim-ce-info-group-title">
                                                Helpful Tips!
                                            </h6>
                                            <p className="claim-ce-info-group-description">
                                                For materials, group them under one service and
                                                input the total cost for all items from your
                                                original estimate. This will streamline the
                                                process and ensure accuracy. Pack Out = Move
                                                Out. Pack Back = Move Back.
                                            </p>
                                        </div>
                                        <div className="claim-ce-info-group">
                                            <p className="claim-ce-info-group-description">
                                                <b>Storage:</b> Select the type of storage
                                                required (e.g., vault or lb) and enter the
                                                estimated storage needed in Qty. Storage is
                                                estimated for one month.
                                            </p>
                                        </div>
                                    </>
                                )}
                            </div>
                        </div>

                        <div className="claim-ce-viewer-head" style={{ padding: '0 30px' }}>
                            <h6 className="claim-ce-viewer-head-title">Original estimate</h6>

                            <div className="claim-ce-viewer-head-actions">
                                <Button
                                    color="primary"
                                    variant="link"
                                    variation={ButtonVariation.LINK}
                                    onClick={handlePreviewFile}
                                    className="claim-ce-viewer-head-actions-preview"
                                >
                                    Preview
                                </Button>
                                <Button
                                    color="primary"
                                    variant="link"
                                    variation={ButtonVariation.LINK}
                                    onClick={handleChangeFile}
                                    className="claim-ce-viewer-head-actions-change"
                                >
                                    Change
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Main content */}
                <div className="claim-ce-main" style={{ flex: '1' }}>
                    {!canSubmit && <Loader className="claim-ce-loader" />}

                    <CustomForm form={form} onSubmit={onSubmit} className="claim-ce-form">
                        {priceBookLoading && <Loader />}

                        {!priceBookLoading && groupNames.length > 0 && (
                            <div className="claim-ce-tab-layout">
                                <div className="claim-ce-tabs">
                                    {groupNames.map((group: string) => (
                                        <div
                                            key={group}
                                            className={`claim-ce-tab${selectedTab === group ? ' selected' : ''}`}
                                            onClick={() => {
                                                console.log('Switching to tab:', group);
                                                setSelectedTab(group);
                                            }}
                                        >
                                            {group}
                                        </div>
                                    ))}
                                </div>
                                <div className="claim-ce-tab-content">
                                    <p>Tab content for {selectedTab} will be implemented here</p>
                                </div>
                            </div>
                        )}

                        {!priceBookLoading && !groupNames.length && (
                            <p>
                                We're updating the list of available services and it'll be ready soon! If
                                you need any help in the meantime, don't hesitate to reach out to us.
                            </p>
                        )}
                    </CustomForm>

                    {/* ESTIMATE SUMMARY Button */}
                    <div className="estimate-summary-button-container">
                        <Button
                            color="primary"
                            variant="solid"
                            onClick={() => setShowConfirmationModal(true)}
                            className="estimate-summary-button"
                            size="large"
                        >
                            ESTIMATE SUMMARY
                        </Button>
                    </div>


                </div>
            </div>

            {showConfirmModal && (
                <Dialog
                    show={showConfirmModal}
                    onClose={() => setShowConfirmModal(false)}
                    onSuccess={() => onClose()}
                    description="If you leave this page, all the information you've entered will be lost. Are you sure you want to continue?"
                />
            )}

            {showConfirmationModal && (
                <EstimateConfirmationModal
                    show={showConfirmationModal}
                    onClose={() => setShowConfirmationModal(false)}
                    onYes={handleConfirmationYes}
                    onNo={handleConfirmationNo}
                />
            )}

            {/* TODO: Add file preview and upload modals */}
        </Content>
    );
};

export default CreateEstimatePage;
