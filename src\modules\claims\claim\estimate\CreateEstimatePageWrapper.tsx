import React, { useState, useEffect } from 'react';
import { useParams, useLocation, useNavigate } from 'react-router-dom';
import CreateEstimatePage from './CreateEstimatePage.tsx';
import { useClaimsContext } from '../../ClaimsWrapper.tsx';
import { useClaimStore } from '../../../../store';
import { Estimate } from '../../../../store/claims/types/Claim.ts';

const CreateEstimatePageWrapper: React.FC = () => {
    const params = useParams();
    const location = useLocation();
    const navigate = useNavigate();
    const { currentClaim } = useClaimsContext();
    const { getClaim } = useClaimStore();
    const [uploadedFile, setUploadedFile] = useState<File | string>('');
    const [estimate, setEstimate] = useState<Estimate | undefined>();

    // Get data from location state if available (when navigating from ClaimPage)
    useEffect(() => {
        if (location.state) {
            const { uploadedFile: stateUploadedFile, estimate: stateEstimate } = location.state as any;
            if (stateUploadedFile) {
                setUploadedFile(stateUploadedFile);
            }
            if (stateEstimate) {
                setEstimate(stateEstimate);
            }
        }
    }, [location.state]);

    // Load claim data if not available
    useEffect(() => {
        if (!currentClaim && params.id) {
            getClaim(params.id).then(() => {
                // Claim will be set via context
            }).catch((error) => {
                console.error('Error loading claim:', error);
                navigate(`/claims/${params.type}`);
            });
        }
    }, [currentClaim, params.id, params.type, getClaim, navigate]);

    if (!currentClaim) {
        return <div>Loading...</div>;
    }

    return (
        <CreateEstimatePage
            uploadedFile={uploadedFile}
            estimate={estimate}
        />
    );
};

export default CreateEstimatePageWrapper;
